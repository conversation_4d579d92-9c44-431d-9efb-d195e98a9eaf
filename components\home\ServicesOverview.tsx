'use client'

import React from 'react'
import { motion } from "framer-motion"
import { Brain, Cpu, GraduationCap, Code, ArrowRight, Sparkles, Zap, Target } from 'lucide-react'
import { Button } from '../ui/button'
import Link from 'next/link'
import { useTranslations } from '@/hooks/useTranslations'

function ServicesOverview() {
  const t = useTranslations('home.servicesOverview')
  const services = [
    {
      id: 'ai-annotation',
      title: t('services.0.title'),
      subtitle: t('services.0.subtitle'),
      description: t('services.0.description'),
      icon: Brain,
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50',
      features: t('services.0.features') as string[],
      stats: t('services.0.stats') as { accuracy: string, speed: string, clients: string }
    },
    {
      id: 'cpu-rental',
      title: t('services.1.title'),
      subtitle: t('services.1.subtitle'),
      description: t('services.1.description'),
      icon: Cpu,
      color: 'from-indigo-500 to-indigo-600',
      bgColor: 'bg-indigo-50',
      features: t('services.1.features') as string[],
      stats: t('services.1.stats') as { performance: string, availability: string, savings: string }
    },
    {
      id: 'education',
      title: t('services.2.title'),
      subtitle: t('services.2.subtitle'),
      description: t('services.2.description'),
      icon: GraduationCap,
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50',
      features: t('services.2.features') as string[],
      stats: t('services.2.stats') as { students: string, courses: string, satisfaction: string }
    },
    {
      id: 'custom-development',
      title: t('services.3.title'),
      subtitle: t('services.3.subtitle'),
      description: t('services.3.description'),
      icon: Code,
      color: 'from-emerald-500 to-emerald-600',
      bgColor: 'bg-emerald-50',
      features: t('services.3.features') as string[],
      stats: t('services.3.stats') as { projects: string, clients: string, satisfaction: string }
    }
  ]

  return (
    <section className="py-24 sm:py-32 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-b from-white via-slate-50/50 to-blue-50/30" />
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-20 left-10 w-72 h-72 rounded-full blur-3xl opacity-20 animate-float" style={{ background: 'linear-gradient(135deg, rgb(59 130 246), rgb(37 99 235))' }} />
        <div className="absolute bottom-20 right-10 w-96 h-96 rounded-full blur-3xl opacity-15 animate-float" style={{ background: 'linear-gradient(135deg, rgb(99 102 241), rgb(59 130 246))', animationDelay: '3s' }} />
      </div>

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        {/* 标题区域 */}
        <motion.div
          className="mx-auto max-w-4xl text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="flex items-center justify-center gap-2 mb-6">
            <Sparkles className="h-6 w-6 text-blue-600" />
            <span className="text-lg font-semibold text-blue-600">{t('title')}</span>
            <Sparkles className="h-6 w-6 text-blue-600" />
          </div>
          <h2 className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl text-gradient-modern mb-8">
            {t('subtitle')}
          </h2>
          <p className="text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto">
            {t('description')}
          </p>
        </motion.div>

        {/* 服务卡片网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-6">
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="group"
            >
              <ServiceCard service={service} index={index} />
            </motion.div>
          ))}
        </div>

        {/* 底部CTA */}
        <motion.div
          className="text-center mt-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <Button asChild className="btn-modern shadow-button-modern group px-8 py-4 text-lg">
            <Link href="/products">
              {t('ctaButton')}
              <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  )
}

// 服务卡片组件
function ServiceCard({ service, index }: { service: any, index: number }) {
  return (
    <div className="relative h-full group">
      <div className="card-modern p-8 h-full hover-glow transition-all duration-500 hover:scale-105">
        {/* 图标和标题 */}
        <div className="flex flex-col items-center text-center mb-8">
          <div className="relative mb-6">
            <div className={`flex h-20 w-20 items-center justify-center rounded-3xl shadow-lg bg-gradient-to-br ${service.color} group-hover:scale-110 transition-transform duration-300`}>
              <service.icon className="h-10 w-10 text-white" />
            </div>
            <div className="absolute inset-0 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'rgba(59, 130, 246, 0.3)' }} />
          </div>
          
          <h3 className="text-2xl font-bold text-slate-800 mb-2 group-hover:text-gradient-modern transition-all duration-300">
            {service.title}
          </h3>
          <p className="text-sm font-medium text-blue-600 mb-4">
            {service.subtitle}
          </p>
        </div>

        {/* 描述 */}
        <p className="text-slate-600 leading-relaxed mb-8 text-center">
          {service.description}
        </p>

        {/* 特性标签 */}
        <div className="flex flex-wrap gap-2 justify-center mb-8">
          {service.features.map((feature: string, idx: number) => (
            <span
              key={feature}
              className="inline-flex items-center rounded-full px-3 py-1.5 text-sm font-medium transition-all duration-200 hover-lift"
              style={{
                backgroundColor: 'rgba(59, 130, 246, 0.08)',
                color: 'rgb(59 130 246)',
                animationDelay: `${idx * 100}ms`
              }}
            >
              {feature}
            </span>
          ))}
        </div>

        {/* 统计数据 */}
        <div className="grid grid-cols-3 gap-4 pt-6 border-t border-slate-100">
          {Object.entries(service.stats).map(([key, value], idx) => (
            <div key={key} className="text-center">
              <div className="text-lg font-bold text-blue-600">{String(value)}</div>
              <div className="text-xs text-slate-500 capitalize">{key}</div>
            </div>
          ))}
        </div>

        {/* 悬停效果底边 */}
        <div className="absolute bottom-0 left-0 right-0 h-1 rounded-b-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: `linear-gradient(90deg, ${service.color.replace('from-', '').replace(' to-', ', ')})` }} />
      </div>
    </div>
  )
}

export default ServicesOverview
